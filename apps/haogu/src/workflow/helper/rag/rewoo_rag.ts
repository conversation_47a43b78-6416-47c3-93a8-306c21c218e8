import { LLM } from 'lib/ai/llm/llm_model'
import { IReasoningRagTool, RagToolExecuteParams, ReasoningRagTool } from './reasoning_rag_tool'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { Config } from 'config'

interface PlanStep {

    reasoning: string
    plan: string
    op: string
    input: RagToolExecuteParams
    fallbacks?: string[]
}


interface Plan {
    steps: PlanStep[]
}


export class RewooRag {
  public static async isNeedReasoning(chatHistory: string, strategy: string, ragContext: string, roundId: string) {
    const template = `# 辅助决策
你是一个辅助决策助手，负责根据对话历史、回复策略与当前检索到的上下文，判断是否需要调用额外背景信息（如课程内容、服务体系、工具功能，实时股票信息等），来支撑生成符合策略的回复。

在做出判断时：
- 关注核心意图的匹配度，不拘泥于具体表述；允许同义、近义、隐含含义和概念泛化（如时间、数量、条件）来进行匹配。
- 若【回复策略】显式或隐式需要澄清/说明交易体系（或体系）时， **直接认为** 需要额外信息，判定为 **YES**。
- 若【回复策略】要求发送具体链接/入口（如课程链接、直播链接、课程回放链接等），但上下文未包含该链接，**直接判定为 YES**。
- 只有在关键信息缺失，且该信息无法从当前上下文或简单推理中得到，而必须依赖 **课程/服务/产品/实时股票信息** 信息时，才返回 **YES**；否则返回 **NO**。
- 不再判定是否需要从客户身上收集信息。

# 回复策略
{{strategy}}

# 对话历史
{{chatHistory}}

# 检索到的上下文
{{ragContext}}

# 输出要求
请严格按照如下 JSON 格式输出
{
  "reason": "说明判定依据",
  "verdict": "YES"（需要额外信息） 或 "NO"(不需要额外信息)
}`

    const promptTemplate = SystemMessagePromptTemplate.fromTemplate(template, { templateFormat: 'mustache' })
    const response = await LLM.predict(
      promptTemplate,
      { meta: { round_id: roundId }, responseJSON: true, reasoningEffort: 'low' },
      { strategy, chatHistory, ragContext }
    )
    this.logMessage('Reason:', response)

    const verifyRes = response.verdict

    return verifyRes === 'YES'

  }

  public static async search(chatHistory: string, strategy: string, chatId: string, roundId: string) {

    const plan = await RewooRag.generatePlan(chatHistory, strategy, await ReasoningRagTool.getTools(), chatId, roundId)
    const evidence = await RewooRag.executePlan(plan)

    this.logMessage('Evidence:', evidence)

    // const solverResult = JSON.parse(await RewooRag.solver(chatHistory, strategy, evidence, roundId))

    // console.log('Solver result:', solverResult)

    // if (solverResult.sufficient) {
    //   return solverResult.summary
    // }

    return evidence
  }

  private static async generatePlan(chatHistory: string, strategy: string, tools: IReasoningRagTool[], chatId: string, roundId: string): Promise<Plan> {
    // Compose a human friendly list of tool names for the prompt
    const toolList = tools
      .map((t) => `* ${t.name}: ${t.description}`)
      .join('\n')
    const plannerPrompt = `你是一个计划专家，需要根据回复策略与客户的聊天记录生成可执行计划。请按以下格式输出 JSON，不要写任何其他文字：

{"steps": [{"reasoning": "<简短阐述为什么需要此步骤>","plan":"<步骤描述>", "op": "<工具键名>", "input": "" }, ...]}

请注意：
1. 将复杂问题拆解为若干个步骤，每个步骤只调用一个工具。
2. 全流程最多安排3个步骤；若一个数据源即可充分回答，则使用1步；若存在不确定性/需验证，优先在不同数据源上补充查询（避免重复同一来源）。
3. op 字段必须是可用工具名称。
4. input 字段是传给工具的字符串。
5. 在选择步骤时，尽可能覆盖不同的数据源/索引类型（例如：结构化检索、非结构化文档、实时信息等），以获得更优答案。

可用工具：
{{toolList}}

回复策略：
{{strategy}}

聊天记录：
{{chatHistory}}
`

    const promptTemplate = SystemMessagePromptTemplate.fromTemplate(plannerPrompt, { templateFormat:'mustache' })

    const response = await LLM.predict(promptTemplate, {
      meta: { round_id: roundId },
      responseJSON: true,
      reasoningEffort: 'low',
      maxTokens: 2000
    },
    { toolList, strategy, chatHistory })

    this.logMessage('Planner response:', response)

    // The LLM returns a JSON string; parse it into the Plan type.  In
    // case of malformed output, we fall back to an empty plan to
    // prevent runtime exceptions.
    try {
      return this.plannerResponseToPlan(response, strategy, chatId, roundId)
    } catch (err) {
      console.warn('Failed to parse planner response', err)
      return { steps: [] }
    }
  }


  private static async executePlan(plan: Plan) {
    let result = ''
    for (const step of plan.steps) {
      const tool = await ReasoningRagTool.getToolByKey(step.op)
      if (!tool) {
        continue
      }
      const toolResult = await tool.execute(step.input)
      result += `
操作:${step.op}
结果:${toolResult}`
    }

    return result
  }


  private static async solver(chatHistory: string, strategy: string, evidence: string, roundId: string) {
    const solverPrompt = `你是“知识整合器”。基于以下信息产出一份供后续回答阶段使用的内部整理。

任务目标：
1) 判断依据现有证据是否足以直接回答用户的核心问题（答案完备性）。
2) 记录整理过程中的关键观察（如证据一致/冲突、范围与边界、假设与限制、缺口等）。
3) 给出聚合后的简明知识总结（可在回答阶段直接引用，不要包含客套话或与用户互动的语句）。

回复策略：
${strategy}

聊天记录：
${chatHistory}

证据：
${evidence}

请按如下严格 JSON 结构输出：
{
  "observation": "这里写出你的关键观察，若有不一致或缺口请点明。",
  "sufficient": true （只要有可用信息（哪怕是部分）就返回 true，否则为 false）,
  "summary": "这里写出聚合后的简明知识总结。"
}`

    return await LLM.predict(solverPrompt, {
      meta: { round_id: roundId },
      responseJSON: true,
      model: 'gpt-5',
    })
  }


  private static async plannerResponseToPlan(jsonData: Record<string, any>, strategy: string, chatId: string, roundId: string) {
    return {
      steps: jsonData.steps.map((step: any) => {
        const reasoning = step.reasoning
        const op = step.op
        const plan = step.plan
        const input = step.input

        return {
          reasoning,
          plan,
          op,
          input: {
            chatId,
            roundId,
            strategy,
            ...input
          },
        }
      })
    } as Plan
  }


  private static logMessage(key: string, data: any) {
    if (Config.setting.localTest) {
      console.log(key, data)
    }
  }
}