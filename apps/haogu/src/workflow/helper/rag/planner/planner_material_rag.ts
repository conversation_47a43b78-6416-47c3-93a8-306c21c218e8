import { PromptTemplate } from '@langchain/core/prompts'
import { DataService } from '../../get_data'
import { LLM } from 'lib/ai/llm/llm_model'
import { MaterialManager } from '../../material_manager'
import logger from 'model/logger/logger'
import { sleep } from 'openai/core'
import { commonMessageSender } from '../../../../config/instance/send_message_instance'


export class PlannerMaterialRag {


  public static async extractMaterial(mainTask: string, chat_id: string, round_id: string) {
    try {
      const timeStr = await this.getTimeInfo(chat_id)

      const res = await LLM.predict(PlannerMaterialRag.extractMaterialFromPlanPrompt(), {
        meta:{
          round_id: round_id
        },
        responseJSON: true
      }, { plan: mainTask, timeStr: timeStr })

      const selectedMaterials = res.selected_materials

      const materialPromises = selectedMaterials.map(async (selectedMaterial: { title: string; category: string }) => {
        const material = await new MaterialManager().searchMaterialByTitle(chat_id, selectedMaterial.title, selectedMaterial.category, 0.7)
        if (material && await new MaterialManager().isValidCourseMaterial(chat_id, material.title)) {
          return `素材标题：${material.title}，素材内容：${material.description}\n`
        }
        return ''
      })

      const materialResults = await Promise.all(materialPromises)
      return materialResults.join('')
    } catch (e) {
      logger.error('获取发送资料',  e)
      return ''
    }
  }


  public static async sendMaterial(chatId: string, titles: string[]) {

    try {
      for (let i = 0; i < titles.length; i++) {
        const material = await new MaterialManager().searchMaterialByTitleOnly(chatId, titles[i])
        if (material) {
          await sleep(3000)
          await commonMessageSender.sendMaterial(chatId, { sourceId:material.source_id })
        }
      }
    } catch (e) {
      logger.error('planner 素材发送失败',  e)
    }



  }



  private static extractMaterialFromPlanPrompt() {

    const prompt = `你是一个“素材调用助手”。  
你的任务是：根据给定的 **目标描述/服务目的**，自动联想出可能需要的素材，并返回对应素材库中素材的 title 列表，用于后续搜索和调用。  

# 目标描述
{{plan}}

# 当前时间
{{timeStr}}

# 素材目录结构
 - 公司介绍相关（可能包含的素材名称：公司好评案例）
 - 6天课程相关（可能包含的素材标题：福利课1:《双线合一核心解析》、福利课2:《四点共振实战应用》、福利课3:《主力锁仓和出货》 、第x节预习视频、第x节课后作业、第x节课后笔记）
 - 交易体系相关
 - 工具相关(可能包含的素材标题：手机/电脑版APP安装方法、多空趋势线指标工具、抄底先锋指标工具、主力进出指标工具)
 - 3360实战班相关
 - 成交相关

# 使用规则：
1. 目标输入可能来自「课程规划器」生成的 task

2. 你需要基于目标的语义，匹配到最相关的素材标题。  
   - **相似或重复的素材只保留一个**，确保输出列表中没有功能重叠的素材。  
   - **每类需求只输出 1 个最能代表的素材**，保证覆盖目标需求即可。  
   - 输出的素材标题必须是一句精简、完整的话，**不得包含括号说明**。

### 以 JSON 格式输出：
{
  "selected_materials":[
    {
      "category":"素材目录",
      "title":"素材标题"
    }
  ]
}`
    return PromptTemplate.fromTemplate(prompt, { templateFormat: 'mustache' })
  }


  private static async getTimeInfo(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (currentTime.day < 1) {
      return '当前上课前'
    }

    const courseInfo = [
      '今天第一节课，四点共振',
      '今天第二节课，双线合一',
      '今天第三节课，抄底先锋',
      '今天第四节课，趋势拐点',
      '今天第五节课，优中选优',
      '今天第六节课，卖点'
    ]

    if (currentTime.day >= 1 && currentTime.day <= 6) {
      return courseInfo[currentTime.day - 1]
    }

    return '当前课程已结束'
  }

}