import { AzureOpenAIClient, CheapOpenAI, OpenAIClient, OpenAIModelName, QwenMax } from './client'
import { BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { ChatPromptTemplate, ParamsFromFString, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { Config } from '../../../config'
import { IterableReadableStream } from '@langchain/core/utils/stream'
import { Runnable, RunnableConfig } from '@langchain/core/runnables'
import { StringHelper } from '../../string'
import { StringOutputParser } from '@langchain/core/output_parsers'
import {
  llmImageChatCounter,
  llmImageChatErrorCounter,
  llmMessagePredictCounter,
  llmMessagePredictErrorCounter
} from './prometheus'
import { UUID } from '../../uuid/uuid'

interface LLMInitParams {
  model?: OpenAIModelName
  maxTokens?: number
  responseJSON?: boolean  // 若设置为 true，系统提示词中必须包含 JSON 字样，如：请严格按照如下 JSON 格式输出
  reasoningEffort?: string
  systemPrompt?: string
  runnableConfig?: RunnableConfig
  promptName?: string
  meta?: Record<string, any>  // 用于 langSmith 记录元信息，方便查询，round_id 对应了 run_id， chat_id 对应了 thread_id
  projectName?: string  // 用于配置 langSmith 日志记录到的项目
}

type JSONOutput<R extends boolean | undefined> =
    R extends true ? Record<string, any> : string

enum ClientType {
  AZURE = 'azure',
  CHEAP = 'cheap',
  OPENAI = 'openai',
  QWEN = 'qwen',
}

/**
 * OpenAI API 封装
 * 注意：
 * 1. 聊天记录需自己维护
 * 2. 尽量不要引入 openai 包，所有功能尽量以 LangChain 实现，保证稳定性
 */
export class LLM<R extends boolean | undefined = undefined> {
  private readonly model: OpenAIModelName
  private readonly maxTokens: number
  private readonly responseJSON: R
  private readonly reasoningEffort?: string
  private readonly systemPrompt?: string
  private readonly runId?: string
  private readonly runnableConfig: RunnableConfig

  constructor(params?: Omit<LLMInitParams, 'responseJSON'> & { responseJSON?: R }) {
    // 特别注意 round_id 必须为 UUID.v4 格式，否则 langSmith 会报错， 做一下兜底
    if (params?.meta?.round_id) {
      if (!UUID.isUUID(params.meta.round_id)) {
        console.warn('Invalid round_id, must be UUID v4:', params.meta.round_id)
        params.meta.round_id = UUID.v4()
      }
    }

    // 标准化 meta
    const meta = {
      ...(params?.meta ?? {}),
      ...(params?.promptName ? { promptName: params.promptName } : {}),
      ...(params?.meta?.name ? { promptName: params.meta.name } : {}),
      ...(params?.meta?.chat_id ? { thread_id: params.meta.chat_id } : {})
    }

    // 设置 LangSmith 环境变量（项目名优先 params，其次配置）
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_PROJECT = params?.projectName ?? Config.setting.projectName
    process.env.LANGCHAIN_TRACING_V2 = 'true'

    // 字段赋值（使用 ?? 提供默认值）
    this.model = params?.model ?? 'gpt-5-mini'
    this.maxTokens = params?.maxTokens ?? 512

    this.reasoningEffort = params?.reasoningEffort ?? 'minimal'
    this.systemPrompt = params?.systemPrompt

    // 处理 runnableConfig 与元数据
    this.runnableConfig = params?.runnableConfig ?? {}
    this.runnableConfig.metadata = meta

    // 提取 runId
    this.runId = params?.meta?.round_id

    this.responseJSON = params?.responseJSON as R
  }

  private createClient(type: ClientType) {
    switch (type) {
      case ClientType.AZURE:
        return AzureOpenAIClient.getClient({
          model: this.model,
          maxTokens: this.maxTokens,
          reasoningEffort: this.reasoningEffort,
        })
      case ClientType.CHEAP:
        return CheapOpenAI.getClient({
          model: this.model,
          maxTokens: this.maxTokens,
          reasoningEffort: this.reasoningEffort,
        })
      case ClientType.OPENAI:
        return OpenAIClient.getClient({
          model: this.model,
        })
      case ClientType.QWEN:
        return QwenMax.getClient()
      default:
        console.warn('Unknown client type:', type)
        // 保底按照 Azure OpenAI
        return AzureOpenAIClient.getClient({
          model: this.model,
          maxTokens: this.maxTokens,
          reasoningEffort: this.reasoningEffort,
        })
    }
  }

  /**
   * 根据 model 参数调整调用优先级
   * @private
   */
  private getClients() {
    let clientOrder: ClientType[]

    if (this.model.startsWith('gpt') && !Config.setting.localTest) {
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.QWEN, ClientType.OPENAI]
    } else if (this.model.startsWith('claude')) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.QWEN, ClientType.OPENAI]
    } else if (this.model.startsWith('qwen-max')) {
      clientOrder = [ClientType.QWEN, ClientType.AZURE, ClientType.CHEAP, ClientType.OPENAI]
    } else if (Config.setting.localTest) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.QWEN, ClientType.OPENAI]
    } else { // 保底配置
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.QWEN, ClientType.OPENAI]
    }

    return clientOrder.map(this.createClient.bind(this))
  }

  private getMetaFromConfig() {
    const md = (this.runnableConfig?.metadata ?? {}) as any
    return {
      promptName: md?.promptName ?? md?.name ?? 'unknown_prompt',
      chat_id: md?.chat_id ?? '',
      round_id: md?.round_id ?? '',
    }
  }

  // ===== 新增：根据 responseJSON 决定是否 JSON.parse 并兜底 =====
  private maybeParseJSON(output: string): string | Record<string, unknown> {
    if (!this.responseJSON) return output

    try {
      return JSON.parse(output) as Record<string, unknown>
    } catch (error) {
      const { promptName, chat_id, round_id } = this.getMetaFromConfig()
      // 按要求打印具体错误原因
      // 例如：logger.error('xxx 解析 JSON 失败:', chat_id, round_id, output, error)
      console.error(`${promptName} 解析 JSON 失败:`, chat_id, round_id, output, error)

      throw error
    }
  }

  /**
   * 使用文本或 PromptTemplate 调用 LLM
   * LLM.predict('Hello, World!')
   * LLM.predict(PromptTemplate.from('Hello, {name}!'), { name: 'World' })
   * @param prompt 文本 或 PromptTemplate, 如果是 PromptTemplate，则使用 params 参数替换模板中的变量
   * @param promptParams
   */
  async predict<T extends string>(
      this: LLM<true>,
      prompt: string | SystemMessagePromptTemplate | Runnable,
      promptParams?: ParamsFromFString<T>
  ): Promise<Record<string, unknown>>
  async predict<T extends string>(
      this: LLM<false | undefined>,
      prompt: string | SystemMessagePromptTemplate | Runnable,
      promptParams?: ParamsFromFString<T>
  ): Promise<string>
  // “桥接”重载：this: LLM<boolean | undefined>（给泛型调用方用）
  async predict<T extends string>(
      this: LLM<boolean | undefined>,
      prompt: string | SystemMessagePromptTemplate | Runnable,
      promptParams?: ParamsFromFString<T>
  ): Promise<string | Record<string, unknown>>


  async predict<T extends string>(
    this: LLM<boolean | undefined>,
    prompt: string | SystemMessagePromptTemplate | Runnable,
    promptParams?: ParamsFromFString<T>
  ): Promise<string | Record<string, unknown>> {
    const clients = this.getClients()
    const parser = new StringOutputParser()

    for (const client of clients) {
      try {
        llmMessagePredictCounter.labels({ bot_id: Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)

        const runnableConfig = this.responseJSON
          ? { ...this.runnableConfig, response_format: { type: 'json_object' } }
          : this.runnableConfig

        let resText: string
        if (typeof prompt === 'string') {
          resText = await client
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(prompt, { runId: this.runId })
        } else if (promptParams) {
          resText = await (prompt as Runnable)
            .pipe(client)
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(promptParams, { runId: this.runId })
        } else {
          return ''
        }

        if (!resText) {
          throw new Error('content filtered')
        }

        // 新增：按需解析 JSON 并返回
        return this.maybeParseJSON(resText)
      } catch (e) {
        llmMessagePredictErrorCounter.labels({ bot_id: Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 简单使用单条文本调用 OpenAI
   */
  public static async predict<T extends string, R extends boolean | undefined = undefined>(
    prompt: string | SystemMessagePromptTemplate | Runnable,
    params?: LLMInitParams & { responseJSON?: R },
    promptParams?: ParamsFromFString<T>
  ): Promise<JSONOutput<R>> {
    const llm = new LLM(params)
    const result = await llm.predict(prompt, promptParams)
    return result as JSONOutput<R>
  }

  private isMessagesContainSystemPrompt(messages: BaseMessage[]): boolean {
    return messages.length > 0 && messages[0].getType() === 'system'
  }

  /**
   * 使用一组消息或 ChatPromptTemplate 调用 LLM，需要自己维护聊天记录
   */
  // A
  async predictMessage<T extends BaseMessage[] | ChatPromptTemplate>(
      this: LLM<true>, messages: T,  params?: T extends ChatPromptTemplate<infer P> ? P : never
  ): Promise<Record<string, unknown>>
  // B
  async predictMessage<T extends BaseMessage[] | ChatPromptTemplate>(
      this: LLM<false | undefined>, messages: T,  params?: T extends ChatPromptTemplate<infer P> ? P : never
  ): Promise<string>
  // C
  async predictMessage<T extends BaseMessage[] | ChatPromptTemplate>(
      this: LLM<boolean | undefined>, messages: T,  params?: T extends ChatPromptTemplate<infer P> ? P : never
  ): Promise<string | Record<string, unknown>>

  async predictMessage<T extends BaseMessage[] | ChatPromptTemplate>(
    messages: T,
    params?: T extends ChatPromptTemplate<infer P> ? P : never
  ): Promise<string | Record<string, unknown>> {
    const chatHistory = messages
    const parser = new StringOutputParser()
    const clients = this.getClients()

    for (const client of clients) {
      try {
        const runnableConfig = this.responseJSON
          ? { ...this.runnableConfig, response_format: { type: 'json_object' } }
          : this.runnableConfig

        let text: string
        if (Array.isArray(chatHistory)) {
          text = await client
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(chatHistory, { runId: this.runId })
        } else {
          text = await (messages as ChatPromptTemplate)
            .pipe(client)
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(params, { runId: this.runId })
        }

        if (!text) {
          throw new Error('content filtered')
        }

        return text
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 使用一组消息调用 OpenAI
   */
  public static async predictMessage<R extends boolean | undefined = undefined>(
    messages: BaseMessage[],
    params?: LLMInitParams & { responseJSON?: R }
  ): Promise<JSONOutput<R>> {
    const llm = new LLM(params)
    const result = await llm.predictMessage(messages)
    return result as JSONOutput<R>
  }

  /**
   * 流式输出（不变）
   */
  async stream(messages: BaseMessage[], systemPrompt?: string): Promise<IterableReadableStream<string>> {
    let chatHistory = this.cleanPromptMessages(messages)
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...messages]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).stream(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)
      }
    }

    throw new Error('All attempts to stream with available clients have failed.')
  }

  cleanPromptMessage(prompt: string) {
    return StringHelper.replaceMultipleBlankLines(prompt).trim()
  }

  async imageChat(imageUrl: string, systemPrompt?: string) {
    const message = new HumanMessage({
      content: [
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    })
    let chatHistory: BaseMessage[] = [message]
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...chatHistory]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(chatHistory)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...chatHistory]
    }
    const parser = new StringOutputParser()
    const clients = this.getClients()

    for (let i = 0; i < clients.length; i++) {
      const client = clients[i]
      try {
        llmImageChatCounter.labels({ bot_id: Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        llmImageChatErrorCounter.labels({ bot_id: Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        console.error(`Error using client ${i} for image processing:`, e)

        if (i === clients.length - 1) {
          throw new Error('Failed to process image with all available clients.')
        }
      }
    }
  }

  /**
   * Prompt 格式化，移除无用空行
   * @param messages
   * @private
   */
  private cleanPromptMessages(messages: BaseMessage[]) {
    messages.forEach((message) => {
      if (typeof message.content === 'string') {
        message.content = this.cleanPromptMessage(message.content)
      }
    })

    return messages
  }
}
